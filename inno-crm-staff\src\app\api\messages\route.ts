import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/database/prisma"
import { z } from "zod"

const createMessageSchema = z.object({
  subject: z.string().min(1, "Subject is required"),
  content: z.string().min(1, "Content is required"),
  recipientType: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS", "SPECIFIC"]),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).default("MEDIUM"),
  sendNow: z.boolean().default(false),
})

const updateMessageSchema = z.object({
  subject: z.string().min(1, "Subject is required").optional(),
  content: z.string().min(1, "Content is required").optional(),
  recipientType: z.enum(["ALL", "STUDENTS", "TEACHERS", "ACADEMIC_MANAGERS", "SPECIFIC"]).optional(),
  recipientIds: z.array(z.string()).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).optional(),
  status: z.enum(["DRAFT", "SENT", "FAILED"]).optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status")
    const priority = searchParams.get("priority")
    const recipientType = searchParams.get("recipientType")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    let where: any = {}

    // For non-admin users, only show their own messages
    if (!["ADMIN", "MANAGER"].includes(session.user.role)) {
      where.senderId = session.user.id
    }

    if (status) {
      where.status = status
    }

    if (priority) {
      where.priority = priority
    }

    if (recipientType) {
      where.recipientType = recipientType
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where,
        skip,
        take: limit,
        include: {
          sender: {
            select: { id: true, name: true, role: true }
          }
        },
        orderBy: { createdAt: "desc" }
      }),
      prisma.message.count({ where })
    ])

    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching messages:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !["ADMIN", "MANAGER", "TEACHER", "ACADEMIC_MANAGER"].includes(session.user.role)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createMessageSchema.parse(body)

    // Validate recipient IDs if specific recipients are selected
    if (validatedData.recipientType === "SPECIFIC" && (!validatedData.recipientIds || validatedData.recipientIds.length === 0)) {
      return NextResponse.json({ error: "Recipient IDs are required for specific recipients" }, { status: 400 })
    }

    // Create message
    const messageData = {
      subject: validatedData.subject,
      content: validatedData.content,
      recipientType: validatedData.recipientType,
      recipientIds: validatedData.recipientIds || [],
      priority: validatedData.priority,
      status: validatedData.sendNow ? "SENT" : "DRAFT",
      sentAt: validatedData.sendNow ? new Date() : null,
      senderId: session.user.id,
    }

    const message = await prisma.message.create({
      data: messageData,
      include: {
        sender: {
          select: { id: true, name: true, role: true }
        }
      }
    })

    // If sending now and recipients include students, broadcast to students server
    if (validatedData.sendNow && (validatedData.recipientType === "STUDENTS" || validatedData.recipientType === "ALL")) {
      try {
        // TODO: Implement inter-server message broadcasting
        console.log("Broadcasting message to students server:", message.id)
      } catch (error) {
        console.error("Error broadcasting message to students:", error)
        // Update message status to failed
        await prisma.message.update({
          where: { id: message.id },
          data: { status: "FAILED" }
        })
      }
    }

    return NextResponse.json(message, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error("Error creating message:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
